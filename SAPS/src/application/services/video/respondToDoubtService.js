import { videoRepository } from '../../../domain/repositories/index.js';
import { NotFoundError, ValidationError } from '../../customErrors/index.js';
import ApiCaller from '../../../infra/ApiHandler/index.js';

/**
 * Service to handle teacher response to student doubts
 * This service:
 * 1. Validates the doubt exists and is pending (status -1)
 * 2. Creates a communication channel for teacher-student chat (which automatically updates doubt status to 0)
 * 3. Returns channel information for frontend to open chat
 */
export async function respondToDoubtService({
  commentId,
  teacherId,
  token
}) {
  console.log(`DEBUG - [RespondToDoubt] Starting response process: commentId=${commentId}, teacherId=${teacherId}`);
  
  try {
    // 1. Validate comment exists and get comment info
    const commentInfo = await videoRepository.getCommentInfo(commentId);
    if (!commentInfo) {
      throw new NotFoundError('Comment', `Comment with id ${commentId} not found`);
    }

    if (!commentInfo.isDoubt) {
      throw new ValidationError('Comment is not a doubt');
    }

    console.log(`DEBUG - [RespondToDoubt] Found doubt: ${commentInfo.content.substring(0, 50)}...`);

    // 2. Create communication channel directly (this will also update doubt status to 0)
    const channelData = {
      name: `Dúvida: ${commentInfo.video?.title || 'Vídeo'} - ${commentInfo.username}`,
      members: [teacherId, commentInfo.userId],
      type: 'CHAT',
      contextType: 'DOUBT',
      contextId: commentId.toString()
    };

    console.log(`DEBUG - [RespondToDoubt] Creating communication channel:`, channelData);

    // Create API caller instance for communication service
    const communicationApiUrl = process.env.CDS_API_URL;
    if (!communicationApiUrl) {
      throw new Error('CDS_API_URL environment variable is not set');
    }

    const apiCaller = new ApiCaller(communicationApiUrl);

    let channelResult;
    try {
      // Call communication service directly to create channel with timeout
      // The createChannel use case will handle updating the doubt status to 0
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Request timeout after 10 seconds')), 10000);
      });

      const apiCallPromise = apiCaller.post('/communication/channels', channelData, {}, token);

      channelResult = await Promise.race([apiCallPromise, timeoutPromise]);
      console.log(`DEBUG - [RespondToDoubt] Successfully created channel:`, channelResult.data?.id);
    } catch (error) {
      console.error(`ERROR - [RespondToDoubt] Communication API failed:`, {
        error: error.message,
        channelData,
        stack: error.stack
      });

      // Handle specific error cases
      if (error.message.includes('already being answered')) {
        throw new ValidationError('This doubt is already being answered by another teacher');
      }

      if (error.message.includes('Doubt not found')) {
        throw new NotFoundError('The doubt could not be found');
      }

      throw new Error(`Failed to create communication channel: ${error.message}`);
    }

    // 3. Return success response with channel info
    return {
      success: true,
      message: 'Teacher response initiated successfully',
      data: {
        commentId,
        channelId: channelResult.data?.id,
        channelName: channelData.name,
        studentId: commentInfo.userId,
        studentName: commentInfo.username,
        videoTitle: commentInfo.video?.title,
        doubtContent: commentInfo.content
      }
    };

  } catch (error) {
    console.error(`ERROR - [RespondToDoubt] Service failed:`, {
      commentId,
      teacherId,
      error: error.message,
      stack: error.stack
    });
    
    // Re-throw known errors
    if (error instanceof NotFoundError || error instanceof ValidationError) {
      throw error;
    }
    
    // Wrap unknown errors
    throw new Error(`Failed to respond to doubt: ${error.message}`);
  }
}
